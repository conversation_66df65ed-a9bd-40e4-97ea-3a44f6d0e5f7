from django.urls import path
from . import views

app_name = 'scheduling'

urlpatterns = [
    # Slot Configuration URLs
    path('configurations/', views.SlotConfigurationListView.as_view(), name='configuration-list'),
    path('configurations/<int:pk>/', views.SlotConfigurationDetailView.as_view(), name='configuration-detail'),
    
    # Working Shift URLs
    path('working-shifts/', views.WorkingShiftListView.as_view(), name='working-shift-list'),
    
    # Holiday Schedule URLs
    path('holidays/', views.HolidayScheduleListView.as_view(), name='holiday-list'),
    
    # Time Slot URLs
    path('slots/', views.TimeSlotListView.as_view(), name='slot-list'),
    path('slots/<uuid:pk>/', views.TimeSlotDetailView.as_view(), name='slot-detail'),
    path('slots/available/', views.get_available_slots, name='available-slots'),
    path('slots/generate/', views.generate_slots, name='generate-slots'),
    path('slots/bulk-update/', views.bulk_update_slots, name='bulk-update-slots'),
    
    # Booking URLs
    path('bookings/', views.SlotBookingListView.as_view(), name='booking-list'),
    path('bookings/create/', views.create_booking, name='create-booking'),
]
