from django.shortcuts import render, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse
from decimal import Decimal
from .models import TaxCategory, GSTRate, TaxConfiguration
from .services import TaxCalculationService


@staff_member_required
def tax_quick_setup(request):
    """
    Quick setup view for tax configuration
    """
    if request.method == 'POST':
        try:
            # Get form data
            category_name = request.POST.get('category_name', 'Home Services')
            cgst_rate = Decimal(request.POST.get('cgst_rate', '9.00'))
            sgst_rate = Decimal(request.POST.get('sgst_rate', '9.00'))
            igst_rate = Decimal(request.POST.get('igst_rate', '18.00'))
            service_charge = Decimal(request.POST.get('service_charge', '2.50'))
            
            # Create or update tax category
            category, created = TaxCategory.objects.get_or_create(
                name=category_name,
                defaults={'description': f'Tax category for {category_name}', 'is_active': True}
            )
            
            # Create or update GST rates
            GSTRate.objects.update_or_create(
                tax_category=category,
                gst_type='CGST',
                defaults={'rate_percentage': cgst_rate, 'is_active': True}
            )
            
            GSTRate.objects.update_or_create(
                tax_category=category,
                gst_type='SGST',
                defaults={'rate_percentage': sgst_rate, 'is_active': True}
            )
            
            GSTRate.objects.update_or_create(
                tax_category=category,
                gst_type='IGST',
                defaults={'rate_percentage': igst_rate, 'is_active': True}
            )
            
            # Create or update tax configuration
            config, created = TaxConfiguration.objects.get_or_create(
                name=f'Default {category_name} Tax Configuration',
                defaults={
                    'description': f'Default tax configuration for {category_name}',
                    'default_tax_category': category,
                    'service_charge_percentage': service_charge,
                    'is_active': True
                }
            )
            
            if not created:
                config.service_charge_percentage = service_charge
                config.is_active = True
                config.save()
            
            # Deactivate other configurations
            TaxConfiguration.objects.filter(is_active=True).exclude(pk=config.pk).update(is_active=False)
            
            messages.success(request, f'Tax configuration updated successfully! CGST: {cgst_rate}%, SGST: {sgst_rate}%, Service Charge: {service_charge}%')
            return redirect('admin:taxation_taxconfiguration_changelist')
            
        except Exception as e:
            messages.error(request, f'Error updating tax configuration: {str(e)}')
    
    # Get current configuration
    current_config = TaxConfiguration.objects.filter(is_active=True).first()
    current_rates = {}
    
    if current_config and current_config.default_tax_category:
        for rate in current_config.default_tax_category.gst_rates.filter(is_active=True):
            current_rates[rate.gst_type] = rate.rate_percentage
    
    context = {
        'current_config': current_config,
        'current_rates': current_rates,
        'title': 'Quick Tax Setup'
    }
    
    return render(request, 'admin/taxation/quick_setup.html', context)


@staff_member_required
def calculate_tax_preview(request):
    """
    AJAX view to preview tax calculation
    """
    if request.method == 'POST':
        try:
            amount = Decimal(request.POST.get('amount', '0'))
            tax_calculation = TaxCalculationService.calculate_total_tax_and_charges(amount)
            
            return JsonResponse({
                'success': True,
                'calculation': {
                    'subtotal': str(tax_calculation['subtotal']),
                    'cgst': str(tax_calculation['cgst']),
                    'sgst': str(tax_calculation['sgst']),
                    'igst': str(tax_calculation['igst']),
                    'service_charge': str(tax_calculation['service_charge']),
                    'total_tax': str(tax_calculation['total_gst']),
                    'grand_total': str(tax_calculation['grand_total'])
                }
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})
