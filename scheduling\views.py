from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from datetime import datetime, timedelta, time
from .models import (
    SlotConfiguration, WorkingShift, HolidaySchedule,
    TimeSlot, SlotBlockage, SlotBooking
)
from .serializers import (
    SlotConfigurationSerializer, WorkingShiftSerializer, HolidayScheduleSerializer,
    TimeSlotSerializer, TimeSlotListSerializer, SlotBlockageSerializer,
    SlotBookingSerializer, CreateBookingSerializer, AvailableSlotsRequestSerializer,
    SlotGenerationSerializer, BulkSlotUpdateSerializer
)
from .services import SlotGenerationService, SlotAvailabilityService


class SlotConfigurationListView(generics.ListCreateAPIView):
    """List and create slot configurations."""
    queryset = SlotConfiguration.objects.all()
    serializer_class = SlotConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        if self.request.user.user_type == 'staff':
            return SlotConfiguration.objects.all()
        return SlotConfiguration.objects.filter(is_active=True)


class SlotConfigurationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete slot configuration."""
    queryset = SlotConfiguration.objects.all()
    serializer_class = SlotConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_permissions(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return [permissions.IsAuthenticated(), permissions.IsAdminUser()]
        return [permissions.IsAuthenticated()]


class WorkingShiftListView(generics.ListCreateAPIView):
    """List and create working shifts."""
    serializer_class = WorkingShiftSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        config_id = self.request.query_params.get('configuration')
        if config_id:
            return WorkingShift.objects.filter(configuration_id=config_id)
        return WorkingShift.objects.all()


class HolidayScheduleListView(generics.ListCreateAPIView):
    """List and create holiday schedules."""
    queryset = HolidaySchedule.objects.filter(is_active=True)
    serializer_class = HolidayScheduleSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = HolidaySchedule.objects.filter(is_active=True)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        
        return queryset.order_by('date')


class TimeSlotListView(generics.ListAPIView):
    """List time slots with filtering."""
    serializer_class = TimeSlotListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = TimeSlot.objects.all()
        
        # Filter by date
        date = self.request.query_params.get('date')
        if date:
            queryset = queryset.filter(date=date)
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by availability
        available_only = self.request.query_params.get('available_only')
        if available_only and available_only.lower() == 'true':
            queryset = queryset.filter(
                status='available',
                current_bookings__lt=models.F('max_bookings'),
                date__gte=timezone.now().date()
            )
        
        return queryset.order_by('date', 'start_time')


class TimeSlotDetailView(generics.RetrieveUpdateAPIView):
    """Retrieve and update time slot."""
    queryset = TimeSlot.objects.all()
    serializer_class = TimeSlotSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_permissions(self):
        if self.request.method in ['PUT', 'PATCH']:
            return [permissions.IsAuthenticated(), permissions.IsAdminUser()]
        return [permissions.IsAuthenticated()]


class SlotBookingListView(generics.ListAPIView):
    """List slot bookings."""
    serializer_class = SlotBookingSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = SlotBooking.objects.all()
        
        # Filter by customer (for customer users)
        if self.request.user.user_type == 'customer':
            queryset = queryset.filter(customer_mobile=self.request.user.mobile)
        
        # Filter by order ID
        order_id = self.request.query_params.get('order_id')
        if order_id:
            queryset = queryset.filter(order_id=order_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(booking_status=status_filter)
        
        # Filter by date
        date = self.request.query_params.get('date')
        if date:
            queryset = queryset.filter(time_slot__date=date)
        
        return queryset.order_by('-booked_at')


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_available_slots(request):
    """Get available time slots for a specific date."""
    serializer = AvailableSlotsRequestSerializer(data=request.query_params)
    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    date = serializer.validated_data['date']
    service_duration = serializer.validated_data.get('service_duration_minutes')
    
    try:
        available_slots = SlotAvailabilityService.get_available_slots(
            date=date,
            service_duration_minutes=service_duration
        )
        
        return Response({
            'success': True,
            'date': date,
            'available_slots': TimeSlotListSerializer(available_slots, many=True).data,
            'total_slots': len(available_slots)
        })
    
    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_booking(request):
    """Create a new slot booking."""
    serializer = CreateBookingSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        with transaction.atomic():
            time_slot = TimeSlot.objects.select_for_update().get(
                id=serializer.validated_data['time_slot_id']
            )
            
            # Double-check availability
            quantity = serializer.validated_data.get('quantity', 1)
            if not time_slot.can_book(quantity):
                return Response({
                    'success': False,
                    'message': 'Time slot is no longer available'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Create booking
            booking = SlotBooking.objects.create(
                time_slot=time_slot,
                order_id=serializer.validated_data['order_id'],
                customer_mobile=serializer.validated_data['customer_mobile'],
                customer_name=serializer.validated_data['customer_name'],
                quantity=quantity,
                service_names=serializer.validated_data['service_names'],
                customer_notes=serializer.validated_data.get('customer_notes', ''),
                booking_status='pending'
            )
            
            # Update slot booking count
            time_slot.current_bookings += quantity
            if time_slot.current_bookings >= time_slot.max_bookings:
                time_slot.status = 'booked'
            
            # Add order ID to booked_orders list
            booked_orders = time_slot.booked_orders or []
            if serializer.validated_data['order_id'] not in booked_orders:
                booked_orders.append(serializer.validated_data['order_id'])
                time_slot.booked_orders = booked_orders
            
            time_slot.save()
            
            return Response({
                'success': True,
                'message': 'Booking created successfully',
                'booking': SlotBookingSerializer(booking).data
            }, status=status.HTTP_201_CREATED)
    
    except TimeSlot.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Time slot not found'
        }, status=status.HTTP_404_NOT_FOUND)
    
    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def generate_slots(request):
    """Generate time slots for a date range."""
    serializer = SlotGenerationSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        config = SlotConfiguration.objects.get(
            id=serializer.validated_data['configuration_id']
        )
        
        generated_count = SlotGenerationService.generate_slots(
            configuration=config,
            start_date=serializer.validated_data['start_date'],
            end_date=serializer.validated_data['end_date']
        )
        
        return Response({
            'success': True,
            'message': f'Generated {generated_count} time slots',
            'configuration': config.name,
            'date_range': {
                'start_date': serializer.validated_data['start_date'],
                'end_date': serializer.validated_data['end_date']
            }
        })
    
    except SlotConfiguration.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Slot configuration not found'
        }, status=status.HTTP_404_NOT_FOUND)
    
    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def bulk_update_slots(request):
    """Bulk update time slots."""
    serializer = BulkSlotUpdateSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        slot_ids = serializer.validated_data['slot_ids']
        action = serializer.validated_data['action']
        reason = serializer.validated_data.get('reason', '')
        
        slots = TimeSlot.objects.filter(id__in=slot_ids)
        
        update_data = {}
        if action == 'block':
            update_data = {
                'status': 'blocked',
                'blocked_reason': reason or 'Manually blocked',
                'blocked_by': request.user.username
            }
        elif action == 'unblock':
            update_data = {
                'status': 'available',
                'blocked_reason': '',
                'blocked_by': ''
            }
        elif action == 'maintenance':
            update_data = {
                'status': 'maintenance',
                'blocked_reason': reason or 'Maintenance period',
                'blocked_by': request.user.username
            }
        elif action == 'available':
            update_data = {
                'status': 'available',
                'blocked_reason': '',
                'blocked_by': ''
            }
        
        updated_count = slots.update(**update_data)
        
        return Response({
            'success': True,
            'message': f'Updated {updated_count} time slots',
            'action': action
        })
    
    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
