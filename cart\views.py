from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON><PERSON><PERSON>ted, AllowAny
from django.shortcuts import get_object_or_404
from django.db import transaction
from catalogue.models import Service
from .models import Cart, CartItem
from .serializers import (
    CartSerializer, CartItemSerializer, AddToCartSerializer,
    UpdateCartItemSerializer, ApplyCouponSerializer, CartSummarySerializer,
    CheckoutValidationSerializer
)


def get_cart(request):
    """
    Helper function to get existing cart for authenticated or anonymous user.
    Returns None if no cart exists.
    """
    if request.user.is_authenticated:
        try:
            cart = Cart.objects.get(user=request.user, is_active=True)
            return cart
        except Cart.DoesNotExist:
            return None
        except Cart.MultipleObjectsReturned:
            # If multiple active carts exist, get the most recent one and deactivate others
            carts = Cart.objects.filter(user=request.user, is_active=True).order_by('-updated_at')
            active_cart = carts.first()
            # Deactivate other carts
            carts.exclude(id=active_cart.id).update(is_active=False)
            return active_cart
    else:
        session_key = request.session.session_key
        if not session_key:
            return None

        try:
            cart = Cart.objects.get(session_key=session_key, is_active=True)
            return cart
        except Cart.DoesNotExist:
            return None
        except Cart.MultipleObjectsReturned:
            # If multiple active carts exist, get the most recent one and deactivate others
            carts = Cart.objects.filter(session_key=session_key, is_active=True).order_by('-updated_at')
            active_cart = carts.first()
            # Deactivate other carts
            carts.exclude(id=active_cart.id).update(is_active=False)
            return active_cart


def create_cart(request):
    """
    Helper function to create a new cart for authenticated or anonymous user.
    Ensures only one active cart per user/session.
    Only call this when actually adding items to cart.
    """
    if request.user.is_authenticated:
        # For authenticated users, get or create the active cart
        cart, created = Cart.objects.get_or_create(
            user=request.user,
            is_active=True,
            defaults={'session_key': None}
        )
    else:
        session_key = request.session.session_key
        if not session_key:
            request.session.create()
            session_key = request.session.session_key

        # For anonymous users, get or create the active cart
        cart, created = Cart.objects.get_or_create(
            session_key=session_key,
            is_active=True,
            defaults={'user': None}
        )

    return cart


def merge_session_cart_to_user(request):
    """
    Merge session cart to user cart when user logs in.
    Call this after user authentication.
    """
    if not request.user.is_authenticated:
        return None

    session_key = request.session.session_key
    if not session_key:
        return None

    # Get session cart
    try:
        session_cart = Cart.objects.get(session_key=session_key, is_active=True, user__isnull=True)
    except Cart.DoesNotExist:
        return None

    # Get or create user cart
    user_cart, created = Cart.objects.get_or_create(
        user=request.user,
        is_active=True,
        defaults={'session_key': None}
    )

    # If user cart was just created, transfer session cart items
    if created:
        # Transfer all items from session cart to user cart
        session_cart.items.update(cart=user_cart)
        # Deactivate session cart
        session_cart.is_active = False
        session_cart.save()
        # Update user cart totals
        user_cart.update_totals()
    else:
        # Merge items from session cart to existing user cart
        for session_item in session_cart.items.all():
            existing_item = user_cart.items.filter(service_id=session_item.service_id).first()
            if existing_item:
                # Update quantity of existing item
                existing_item.quantity += session_item.quantity
                existing_item.save()
            else:
                # Transfer item to user cart
                session_item.cart = user_cart
                session_item.save()

        # Deactivate session cart
        session_cart.is_active = False
        session_cart.save()
        # Update user cart totals
        user_cart.update_totals()

    return user_cart


def get_or_create_cart(request):
    """
    Helper function to get or create cart for authenticated or anonymous user.
    Use this only when you need to ensure a cart exists (like when adding items).
    """
    return create_cart(request)


class CartDetailView(generics.RetrieveAPIView):
    """
    Get current user's cart details.
    Returns empty cart structure if no cart exists.
    """
    serializer_class = CartSerializer
    permission_classes = [AllowAny]

    def get_object(self):
        cart = get_cart(self.request)
        if cart is None:
            # Return a mock empty cart structure instead of creating a real cart
            return None
        return cart

    def retrieve(self, request, *args, **kwargs):
        cart = self.get_object()
        if cart is None:
            # Return empty cart response without creating database record
            return Response({
                'id': None,
                'user': request.user.id if request.user.is_authenticated else None,
                'session_key': request.session.session_key,
                'created_at': None,
                'updated_at': None,
                'is_active': True,
                'sub_total': '0.00',
                'tax_amount': '0.00',
                'discount_amount': '0.00',
                'minimum_order_fee_applied': '0.00',
                'total_amount': '0.00',
                'coupon_code_applied': None,
                # Add separate GST breakdown fields for empty cart
                'cgst_amount': '0.00',
                'sgst_amount': '0.00',
                'igst_amount': '0.00',
                'ugst_amount': '0.00',
                'service_charge': '0.00',
                'tax_breakdown': [],
                'items': [],
                'items_count': 0,
                'unique_services_count': 0
            })

        serializer = self.get_serializer(cart)
        return Response(serializer.data)


class CartSummaryView(generics.RetrieveAPIView):
    """
    Get cart summary (totals only).
    Returns empty summary if no cart exists.
    """
    serializer_class = CartSummarySerializer
    permission_classes = [AllowAny]

    def get_object(self):
        cart = get_cart(self.request)
        if cart is None:
            return None
        return cart

    def retrieve(self, request, *args, **kwargs):
        cart = self.get_object()
        if cart is None:
            # Return empty cart summary without creating database record
            return Response({
                'id': None,
                'sub_total': '0.00',
                'tax_amount': '0.00',
                'discount_amount': '0.00',
                'minimum_order_fee_applied': '0.00',
                'total_amount': '0.00',
                'coupon_code_applied': None,
                'items_count': 0,
                'unique_services_count': 0,
                'updated_at': None
            })

        serializer = self.get_serializer(cart)
        return Response(serializer.data)


@api_view(['POST'])
@permission_classes([AllowAny])
def add_to_cart(request):
    """
    Add a service to the cart.
    """
    cart = get_or_create_cart(request)

    serializer = AddToCartSerializer(
        data=request.data,
        context={'cart': cart}
    )

    if serializer.is_valid():
        service_id = serializer.validated_data['service_id']
        quantity = serializer.validated_data['quantity']

        try:
            service = Service.objects.get(id=service_id, is_active=True)

            # Check if item already exists
            existing_item = CartItem.objects.filter(
                cart=cart,
                service_id=service_id
            ).first()

            if existing_item:
                # Update quantity
                existing_item.quantity += quantity
                existing_item.save()
                cart_item = existing_item
            else:
                # Create new item with current pricing and service details
                current_price = service.get_current_price()
                discount = service.base_price - current_price if service.discount_price else 0

                cart_item = CartItem.objects.create(
                    cart=cart,
                    service_id=service_id,
                    service_title=service.title,
                    service_image_url=service.image.url if service.image else None,
                    quantity=quantity,
                    price_at_add=service.base_price,
                    discount_at_add=discount
                )

            # Refresh cart from database to get updated totals
            cart.refresh_from_db()

            # Return updated cart
            cart_serializer = CartSerializer(cart)
            return Response({
                'success': True,
                'message': f'Added {service.title} to cart',
                'cart': cart_serializer.data
            }, status=status.HTTP_201_CREATED)

        except Service.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Service not found'
            }, status=status.HTTP_404_NOT_FOUND)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['PUT'])
@permission_classes([AllowAny])
def update_cart_item(request, item_id):
    """
    Update quantity of a cart item.
    """
    cart = get_cart(request)
    if cart is None:
        return Response({
            'success': False,
            'error': 'No active cart found'
        }, status=status.HTTP_404_NOT_FOUND)

    try:
        cart_item = CartItem.objects.get(id=item_id, cart=cart)
    except CartItem.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Cart item not found'
        }, status=status.HTTP_404_NOT_FOUND)

    serializer = UpdateCartItemSerializer(data=request.data)

    if serializer.is_valid():
        cart_item.quantity = serializer.validated_data['quantity']
        cart_item.save()

        cart_serializer = CartSerializer(cart)
        return Response({
            'success': True,
            'message': 'Cart item updated',
            'cart': cart_serializer.data
        })

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
@permission_classes([AllowAny])
def remove_cart_item(request, item_id):
    """
    Remove an item from the cart.
    """
    cart = get_cart(request)
    if cart is None:
        return Response({
            'success': False,
            'error': 'No active cart found'
        }, status=status.HTTP_404_NOT_FOUND)

    try:
        cart_item = CartItem.objects.get(id=item_id, cart=cart)
        service_title = cart_item.service_title
        cart_item.delete()

        cart_serializer = CartSerializer(cart)
        return Response({
            'success': True,
            'message': f'Removed {service_title} from cart',
            'cart': cart_serializer.data
        })
    except CartItem.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Cart item not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['DELETE'])
@permission_classes([AllowAny])
def clear_cart(request):
    """
    Clear all items from the cart.
    """
    cart = get_cart(request)
    if cart is None:
        return Response({
            'success': False,
            'error': 'No active cart found'
        }, status=status.HTTP_404_NOT_FOUND)

    cart.items.all().delete()
    cart.update_totals()

    return Response({
        'success': True,
        'message': 'Cart cleared successfully'
    })


@api_view(['POST'])
@permission_classes([AllowAny])
def apply_coupon(request):
    """
    Apply a coupon code to the cart.
    This will integrate with the Coupon & Discount Service.
    """
    cart = get_cart(request)
    if cart is None:
        return Response({
            'success': False,
            'error': 'No active cart found. Add items to cart first.'
        }, status=status.HTTP_404_NOT_FOUND)

    serializer = ApplyCouponSerializer(data=request.data)

    if serializer.is_valid():
        coupon_code = serializer.validated_data['coupon_code']

        # TODO: Integrate with Coupon & Discount Service
        # For now, just store the coupon code
        cart.coupon_code_applied = coupon_code
        cart.save()

        cart_serializer = CartSerializer(cart)
        return Response({
            'success': True,
            'message': f'Coupon {coupon_code} applied successfully',
            'cart': cart_serializer.data
        })

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def remove_coupon(request):
    """
    Remove applied coupon from cart.
    """
    cart = get_cart(request)
    if cart is None:
        return Response({
            'success': False,
            'error': 'No active cart found'
        }, status=status.HTTP_404_NOT_FOUND)

    if cart.coupon_code_applied:
        cart.coupon_code_applied = None
        cart.discount_amount = 0
        cart.save()

        cart_serializer = CartSerializer(cart)
        return Response({
            'success': True,
            'message': 'Coupon removed successfully',
            'cart': cart_serializer.data
        })

    return Response({
        'success': False,
        'message': 'No coupon applied to cart'
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def debug_cart_info(request):
    """
    Debug endpoint to check cart information for current user.
    """
    user = request.user

    # Get all carts for this user
    all_user_carts = Cart.objects.filter(user=user)
    active_user_carts = Cart.objects.filter(user=user, is_active=True)

    debug_info = {
        'user_id': user.id,
        'user_type': getattr(user, 'user_type', 'unknown'),
        'total_carts': all_user_carts.count(),
        'active_carts': active_user_carts.count(),
        'carts': []
    }

    for cart in all_user_carts:
        cart_info = {
            'cart_id': cart.id,
            'is_active': cart.is_active,
            'items_count': cart.items.count(),
            'total_amount': str(cart.total_amount),
            'created_at': cart.created_at,
            'updated_at': cart.updated_at,
            'items': []
        }

        for item in cart.items.all():
            cart_info['items'].append({
                'service_id': item.service_id,
                'service_title': item.service_title,
                'quantity': item.quantity,
                'price': str(item.price_at_add)
            })

        debug_info['carts'].append(cart_info)

    return Response({
        'success': True,
        'debug_info': debug_info
    })
