from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON><PERSON><PERSON>ted, IsAdminUser
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.conf import settings
from django.utils import timezone
from decimal import Decimal
import razorpay
import hashlib
import hmac
import json
from .models import (
    PaymentConfiguration, PaymentTransaction, RazorpayPayment,
    CODPayment, PaymentRefund, PaymentWebhook
)
from .serializers import (
    PaymentTransactionSerializer, InitiatePaymentSerializer,
    PaymentVerificationSerializer, RefundSerializer, CODConfirmationSerializer,
    InitiateRefundSerializer, PaymentConfigurationSerializer
)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def initiate_payment(request):
    """
    Initiate payment for cart items (Payment First Flow).
    Order will be created after successful payment.
    """
    cart_id = request.data.get('cart_id')
    amount = request.data.get('amount')
    payment_method = request.data.get('payment_method', 'razorpay')
    currency = request.data.get('currency', 'INR')
    order_data = request.data.get('order_data', {})

    if not cart_id or not amount:
        return Response({
            'success': False,
            'message': 'Cart ID and amount are required'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Validate cart exists and belongs to user
        from cart.models import Cart
        cart = Cart.objects.get(id=cart_id, user=request.user, is_active=True)

        if not cart.items.exists():
            return Response({
                'success': False,
                'message': 'Cart is empty'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create payment transaction (no order yet - will be created after payment)
        transaction = PaymentTransaction.objects.create(
            user=request.user,
            payment_method=payment_method,
            amount=amount,
            currency=currency,
            status='initiated',
            # Store cart and order data for later order creation
            metadata={
                'cart_id': cart_id,
                'order_data': order_data
            }
        )

        if payment_method == 'razorpay':
            # Get payment configuration
            payment_config = PaymentConfiguration.get_active_config()

            if not payment_config.enable_razorpay:
                return Response({
                    'success': False,
                    'message': 'Razorpay payments are currently disabled'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get Razorpay credentials
            credentials = payment_config.get_razorpay_credentials()
            if not credentials['key_id'] or not credentials['key_secret']:
                return Response({
                    'success': False,
                    'message': 'Razorpay is not properly configured'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Initialize Razorpay client
            client = razorpay.Client(
                auth=(credentials['key_id'], credentials['key_secret'])
            )

            # Create Razorpay order
            razorpay_order = client.order.create({
                'amount': int(float(amount) * 100),  # Amount in paise
                'currency': currency,
                'receipt': transaction.transaction_id,
                'payment_capture': 1
            })

            # Create Razorpay payment record
            RazorpayPayment.objects.create(
                transaction=transaction,
                razorpay_order_id=razorpay_order['id']
            )

            transaction.gateway_transaction_id = razorpay_order['id']
            transaction.status = 'pending'
            transaction.save()

            # Return response in the format expected by frontend
            return Response({
                'success': True,
                'transaction_id': transaction.transaction_id,
                'payment_gateway_data': {
                    'key': credentials['key_id'],
                    'amount': int(float(amount) * 100),  # Amount in paise for frontend
                    'currency': currency,
                    'razorpay_order_id': razorpay_order['id'],
                    'name': 'Home Services',
                    'description': f'Cart Payment - ₹{amount}'
                },
                'environment': payment_config.active_environment
            })

        elif payment_method == 'cod':
            # Get payment configuration
            payment_config = PaymentConfiguration.get_active_config()

            if not payment_config.enable_cod:
                return Response({
                    'success': False,
                    'message': 'Cash on Delivery is currently disabled'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check minimum order value for COD
            if float(amount) < payment_config.cod_minimum_order:
                return Response({
                    'success': False,
                    'message': f'Minimum order value for COD is ₹{payment_config.cod_minimum_order}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Calculate COD charges
            from decimal import Decimal
            cod_charges = (Decimal(str(amount)) * payment_config.cod_charge_percentage) / Decimal('100.00')
            total_amount = Decimal(str(amount)) + cod_charges

            # Update transaction amount to include COD charges
            transaction.amount = total_amount
            transaction.save()

            # Create COD payment record
            CODPayment.objects.create(transaction=transaction)

            transaction.status = 'pending'
            transaction.save()

            return Response({
                'success': True,
                'transaction_id': transaction.transaction_id,
                'message': 'COD payment initiated successfully',
                'cod_charges': float(cod_charges),
                'total_amount': float(total_amount),
                'cart_id': cart_id
            })

    except Cart.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Cart not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Payment initiation failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def razorpay_callback(request):
    """
    Handle Razorpay payment callback.
    """
    serializer = PaymentVerificationSerializer(data=request.data)

    if serializer.is_valid():
        transaction_id = serializer.validated_data['transaction_id']
        razorpay_payment_id = serializer.validated_data.get('razorpay_payment_id')
        razorpay_order_id = serializer.validated_data.get('razorpay_order_id')
        razorpay_signature = serializer.validated_data.get('razorpay_signature')

        try:
            transaction = PaymentTransaction.objects.get(
                transaction_id=transaction_id,
                user=request.user
            )

            # Get payment configuration and verify signature
            payment_config = PaymentConfiguration.get_active_config()
            credentials = payment_config.get_razorpay_credentials()

            client = razorpay.Client(
                auth=(credentials['key_id'], credentials['key_secret'])
            )

            # Verify payment signature
            params_dict = {
                'razorpay_order_id': razorpay_order_id,
                'razorpay_payment_id': razorpay_payment_id,
                'razorpay_signature': razorpay_signature
            }

            try:
                client.utility.verify_payment_signature(params_dict)

                # Update transaction
                transaction.gateway_payment_id = razorpay_payment_id
                transaction.gateway_signature = razorpay_signature
                transaction.mark_success()

                # Update Razorpay payment record
                razorpay_payment = transaction.razorpay_details
                razorpay_payment.razorpay_payment_id = razorpay_payment_id
                razorpay_payment.razorpay_signature = razorpay_signature
                razorpay_payment.save()

                # Create order after successful payment verification
                order_created = False
                order_data = None

                # Get cart and order data from transaction metadata
                if transaction.metadata and 'cart_id' in transaction.metadata:
                    try:
                        from cart.models import Cart
                        from orders.models import Order, OrderItem, OrderStatusHistory
                        from catalogue.models import Service

                        cart_id = transaction.metadata['cart_id']
                        cart = Cart.objects.get(id=cart_id, user=request.user, is_active=True)

                        if cart.items.exists():
                            order_metadata = transaction.metadata.get('order_data', {})

                            # Create order with payment already confirmed
                            order = Order.objects.create(
                                customer=request.user,
                                subtotal=cart.sub_total,
                                tax_amount=cart.tax_amount,
                                discount_amount=cart.discount_amount,
                                minimum_order_fee=cart.minimum_order_fee_applied,
                                total_amount=cart.total_amount,
                                coupon_code=cart.coupon_code_applied,
                                coupon_discount=cart.discount_amount,
                                delivery_address=order_metadata.get('delivery_address', {}),
                                payment_method=transaction.payment_method,
                                payment_status='paid',
                                payment_id=razorpay_payment_id,
                                payment_signature=razorpay_signature,
                                scheduled_date=order_metadata.get('scheduled_date'),
                                scheduled_time_slot=order_metadata.get('scheduled_time_slot'),
                                customer_notes=order_metadata.get('customer_notes', ''),
                                status='confirmed'  # Order is confirmed since payment is successful
                            )

                            # Create order items from cart items
                            for cart_item in cart.items.all():
                                try:
                                    service = Service.objects.using('catalogue_db').get(id=cart_item.service_id)
                                    service_title = service.title
                                    estimated_duration = getattr(service, 'time_to_complete', None)
                                except Service.DoesNotExist:
                                    service_title = cart_item.service_title
                                    estimated_duration = None

                                OrderItem.objects.create(
                                    order=order,
                                    service_id=cart_item.service_id,
                                    service_title=service_title,
                                    quantity=cart_item.quantity,
                                    unit_price=cart_item.price_at_add,
                                    discount_per_unit=cart_item.discount_at_add,
                                    total_price=cart_item.get_total_price(),
                                    estimated_duration=estimated_duration
                                )

                            # Update transaction with order reference
                            transaction.order_id = str(order.id)
                            transaction.save()

                            # Create status history
                            OrderStatusHistory.objects.create(
                                order=order,
                                new_status='confirmed',
                                changed_by=request.user,
                                reason='Order created after successful payment'
                            )

                            # Mark cart as inactive
                            cart.is_active = False
                            cart.save()

                            order_created = True
                            from orders.serializers import OrderSerializer
                            order_data = OrderSerializer(order).data

                    except Exception as e:
                        # Log the error but don't fail the payment verification
                        print(f"Error creating order after payment: {str(e)}")

                return Response({
                    'success': True,
                    'message': 'Payment verified successfully',
                    'transaction': PaymentTransactionSerializer(transaction).data,
                    'order_created': order_created,
                    'order': order_data
                })

            except razorpay.errors.SignatureVerificationError:
                transaction.mark_failed('Invalid payment signature', 'SIGNATURE_VERIFICATION_FAILED')
                return Response({
                    'success': False,
                    'message': 'Payment verification failed'
                }, status=status.HTTP_400_BAD_REQUEST)

        except PaymentTransaction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Transaction not found'
            }, status=status.HTTP_404_NOT_FOUND)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cod_confirm(request):
    """
    Confirm Cash on Delivery payment (for service providers).
    """
    serializer = CODConfirmationSerializer(data=request.data)

    if serializer.is_valid():
        transaction_id = serializer.validated_data['transaction_id']
        collected_amount = serializer.validated_data['collected_amount']
        collection_notes = serializer.validated_data.get('collection_notes', '')

        try:
            transaction = PaymentTransaction.objects.get(
                transaction_id=transaction_id,
                payment_method='cod'
            )

            # Only allow service provider or staff to confirm COD
            if request.user.user_type not in ['provider', 'staff']:
                return Response({
                    'success': False,
                    'message': 'Only service providers or staff can confirm COD payments'
                }, status=status.HTTP_403_FORBIDDEN)

            # Update COD payment
            cod_payment = transaction.cod_details
            cod_payment.mark_collected(collected_amount, request.user, collection_notes)

            # Update order status
            from orders.models import Order
            order = Order.objects.get(id=transaction.order_id)
            order.payment_status = 'paid'
            if order.status == 'pending':
                order.status = 'confirmed'
            order.save()

            return Response({
                'success': True,
                'message': 'COD payment confirmed successfully',
                'transaction': PaymentTransactionSerializer(transaction).data
            })

        except PaymentTransaction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Transaction not found'
            }, status=status.HTTP_404_NOT_FOUND)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def payment_status(request, transaction_id):
    """
    Get payment status for a transaction.
    """
    try:
        if request.user.user_type == 'staff':
            transaction = PaymentTransaction.objects.get(transaction_id=transaction_id)
        else:
            transaction = PaymentTransaction.objects.get(
                transaction_id=transaction_id,
                user=request.user
            )

        return Response({
            'success': True,
            'transaction': PaymentTransactionSerializer(transaction).data
        })

    except PaymentTransaction.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Transaction not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_payment(request):
    """
    Verify payment signature and update status.
    """
    # This is handled by razorpay_callback, but keeping for compatibility
    return razorpay_callback(request)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def initiate_refund(request):
    """
    Initiate refund for a payment.
    """
    serializer = InitiateRefundSerializer(data=request.data)

    if serializer.is_valid():
        transaction_id = serializer.validated_data['transaction_id']
        amount = serializer.validated_data['amount']
        reason = serializer.validated_data['reason']

        try:
            transaction = PaymentTransaction.objects.get(transaction_id=transaction_id)

            if transaction.status != 'success':
                return Response({
                    'success': False,
                    'message': 'Can only refund successful transactions'
                }, status=status.HTTP_400_BAD_REQUEST)

            if amount > transaction.amount:
                return Response({
                    'success': False,
                    'message': 'Refund amount cannot exceed transaction amount'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create refund record
            refund = PaymentRefund.objects.create(
                transaction=transaction,
                amount=amount,
                reason=reason,
                initiated_by=request.user
            )

            # Process refund based on payment method
            if transaction.payment_method == 'razorpay':
                try:
                    client = razorpay.Client(
                        auth=(
                            getattr(settings, 'RAZORPAY_KEY_ID', ''),
                            getattr(settings, 'RAZORPAY_KEY_SECRET', '')
                        )
                    )

                    refund_response = client.payment.refund(
                        transaction.gateway_payment_id,
                        {
                            'amount': int(amount * 100),  # Amount in paise
                            'speed': 'normal'
                        }
                    )

                    refund.gateway_refund_id = refund_response['id']
                    refund.status = 'processing'
                    refund.gateway_response = refund_response
                    refund.save()

                except Exception as e:
                    refund.status = 'failed'
                    refund.save()
                    return Response({
                        'success': False,
                        'message': f'Refund initiation failed: {str(e)}'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            elif transaction.payment_method == 'cod':
                # For COD, mark as completed immediately
                refund.status = 'completed'
                refund.processed_at = timezone.now()
                refund.save()

            # Update transaction refund amount
            transaction.refund_amount += amount
            if transaction.refund_amount >= transaction.amount:
                transaction.status = 'refunded'
                transaction.refunded_at = timezone.now()
            transaction.save()

            return Response({
                'success': True,
                'message': 'Refund initiated successfully',
                'refund': RefundSerializer(refund).data
            })

        except PaymentTransaction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Transaction not found'
            }, status=status.HTTP_404_NOT_FOUND)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def refund_status(request, refund_id):
    """
    Get refund status.
    """
    try:
        if request.user.user_type == 'staff':
            refund = PaymentRefund.objects.get(refund_id=refund_id)
        else:
            refund = PaymentRefund.objects.get(
                refund_id=refund_id,
                transaction__user=request.user
            )

        return Response({
            'success': True,
            'refund': RefundSerializer(refund).data
        })

    except PaymentRefund.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Refund not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
def razorpay_webhook(request):
    """
    Handle Razorpay webhooks.
    """
    try:
        # Verify webhook signature
        webhook_signature = request.META.get('HTTP_X_RAZORPAY_SIGNATURE')
        webhook_secret = getattr(settings, 'RAZORPAY_WEBHOOK_SECRET', '')

        if webhook_secret:
            expected_signature = hmac.new(
                webhook_secret.encode('utf-8'),
                request.body,
                hashlib.sha256
            ).hexdigest()

            if webhook_signature != expected_signature:
                return Response({'status': 'invalid signature'}, status=400)

        # Parse webhook data
        webhook_data = json.loads(request.body)
        event_type = webhook_data.get('event')

        # Create webhook record
        webhook = PaymentWebhook.objects.create(
            source='razorpay',
            event_type=event_type,
            payload=webhook_data,
            headers=dict(request.META)
        )

        # Process webhook based on event type
        if event_type == 'payment.captured':
            # Handle successful payment
            payment_data = webhook_data.get('payload', {}).get('payment', {}).get('entity', {})
            order_id = payment_data.get('order_id')

            try:
                razorpay_payment = RazorpayPayment.objects.get(razorpay_order_id=order_id)
                transaction = razorpay_payment.transaction

                if transaction.status != 'success':
                    transaction.mark_success(payment_data)
                    razorpay_payment.webhook_verified = True
                    razorpay_payment.save()

                webhook.transaction = transaction
                webhook.processed = True
                webhook.save()

            except RazorpayPayment.DoesNotExist:
                webhook.processing_error = f"Payment not found for order_id: {order_id}"
                webhook.save()

        elif event_type == 'refund.processed':
            # Handle refund completion
            refund_data = webhook_data.get('payload', {}).get('refund', {}).get('entity', {})
            payment_id = refund_data.get('payment_id')

            try:
                transaction = PaymentTransaction.objects.get(gateway_payment_id=payment_id)
                refund = transaction.refunds.filter(
                    gateway_refund_id=refund_data.get('id')
                ).first()

                if refund:
                    refund.status = 'completed'
                    refund.processed_at = timezone.now()
                    refund.save()

                webhook.transaction = transaction
                webhook.processed = True
                webhook.save()

            except PaymentTransaction.DoesNotExist:
                webhook.processing_error = f"Transaction not found for payment_id: {payment_id}"
                webhook.save()

        return Response({'status': 'ok'})

    except Exception as e:
        return Response({'status': 'error', 'message': str(e)}, status=500)


class TransactionListView(generics.ListAPIView):
    """
    List payment transactions.
    """
    serializer_class = PaymentTransactionSerializer
    permission_classes = [IsAdminUser]

    def get_queryset(self):
        return PaymentTransaction.objects.all().select_related('user').order_by('-created_at')


class TransactionDetailView(generics.RetrieveAPIView):
    """
    Get transaction details.
    """
    serializer_class = PaymentTransactionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if self.request.user.user_type == 'staff':
            return PaymentTransaction.objects.all()
        else:
            return PaymentTransaction.objects.filter(user=self.request.user)


@api_view(['GET'])
@permission_classes([])  # Public endpoint
def payment_configuration(request):
    """
    Get current payment configuration (public endpoint for frontend).
    """
    config = PaymentConfiguration.get_active_config()

    # Return only public configuration data
    return Response({
        'success': True,
        'configuration': {
            'enable_razorpay': config.enable_razorpay,
            'enable_cod': config.enable_cod,
            'cod_charge_percentage': config.cod_charge_percentage,
            'cod_minimum_order': config.cod_minimum_order,
            'environment': config.active_environment,
            'razorpay_key_id': config.get_razorpay_credentials()['key_id'] if config.enable_razorpay else None
        }
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def calculate_partial_payment(request):
    """
    Calculate partial payment amount for services.
    """
    try:
        service_items = request.data.get('items', [])
        if not service_items:
            return Response({
                'success': False,
                'message': 'No service items provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        from catalogue.models import Service
        total_amount = Decimal('0.00')
        partial_amount = Decimal('0.00')
        items_breakdown = []

        for item in service_items:
            service_id = item.get('service_id')
            quantity = int(item.get('quantity', 1))

            try:
                service = Service.objects.get(id=service_id, is_active=True)

                item_total = service.get_current_price() * quantity
                item_partial = service.get_partial_payment_amount(quantity)
                item_remaining = service.get_remaining_payment_amount(quantity)

                total_amount += item_total
                partial_amount += item_partial

                items_breakdown.append({
                    'service_id': service.id,
                    'service_title': service.title,
                    'quantity': quantity,
                    'unit_price': service.get_current_price(),
                    'total_price': item_total,
                    'requires_partial_payment': service.requires_partial_payment,
                    'partial_payment_amount': item_partial,
                    'remaining_amount': item_remaining
                })

            except Service.DoesNotExist:
                return Response({
                    'success': False,
                    'message': f'Service with ID {service_id} not found'
                }, status=status.HTTP_404_NOT_FOUND)

        remaining_amount = total_amount - partial_amount

        return Response({
            'success': True,
            'calculation': {
                'total_amount': total_amount,
                'partial_payment_amount': partial_amount,
                'remaining_amount': remaining_amount,
                'requires_partial_payment': partial_amount > 0,
                'items_breakdown': items_breakdown
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Calculation failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
