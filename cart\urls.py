from django.urls import path
from . import views

app_name = 'cart'

urlpatterns = [
    # Cart management
    path('', views.CartDetailView.as_view(), name='cart-detail'),
    path('summary/', views.CartSummaryView.as_view(), name='cart-summary'),
    
    # Cart operations
    path('add/', views.add_to_cart, name='add-to-cart'),
    path('items/<int:item_id>/update/', views.update_cart_item, name='update-cart-item'),
    path('items/<int:item_id>/remove/', views.remove_cart_item, name='remove-cart-item'),
    path('clear/', views.clear_cart, name='clear-cart'),
    
    # Coupon management
    path('coupon/apply/', views.apply_coupon, name='apply-coupon'),
    path('coupon/remove/', views.remove_coupon, name='remove-coupon'),

    # Debug endpoint
    path('debug/', views.debug_cart_info, name='debug-cart'),
]
